<script setup lang="ts">
import SearchBar from '@/components/molecules/SearchBar.vue';
import { onMounted, computed, ref } from 'vue';
import ViewOptionsToggle from '@/components/organisms/ViewOptionsToggle.vue';
import ProcessCard from '@/components/molecules/ProcessCard.vue';
import ProcessTile from '@/components/molecules/ProcessTile.vue';
import Pagination from '@/components/atoms/Pagination.vue';
import QuorumEditModal from '@/components/molecules/QuorumEditModal.vue';
import Button from '@/components/atoms/Button.vue';
import { useRoute } from 'vue-router';
import type { Process } from '@/models/types';
import { useProcesses } from '@/hooks/process/useProcesses';
import { useToast } from 'vue-toast-notification';

const route = useRoute();
const auditorID = ref<number>(parseInt(route.params.auditorID as string));
const toast = useToast();

const { getRouterLinkByStatusAndID, isLoading, processes, page, totalPages, fetchPageData, handleSearch, handleNextPage, handlePreviousPage, handleGoToPage, handleGetButtonTextByStatus, handleUpdateProcessQuorum } = useProcesses(
    { auditorID: auditorID }
);

const headers = computed(() => ['ID', 'Fecha Inicio - Fin', 'Institución', 'Funcionarios', 'Ciudad', 'Contacto', 'Acción']);

// Estado para el modal de edición de quorum
const isQuorumModalOpen = ref(false);
const selectedProcess = ref<Process | null>(null);

const openQuorumModal = (process: Process) => {
    selectedProcess.value = process;
    isQuorumModalOpen.value = true;
};

const closeQuorumModal = () => {
    isQuorumModalOpen.value = false;
    selectedProcess.value = null;
};

const handleSaveQuorum = async (processId: number, quorum: number) => {
    try {
        await handleUpdateProcessQuorum(processId, quorum);
        // Actualizar el proceso en la lista local
        const processIndex = processes.value.findIndex(p => p.id === processId);
        if (processIndex !== -1) {
            processes.value[processIndex].approvalThreshold = quorum;
        }
        toast.success('Quorum actualizado correctamente');
    } catch (error) {
        console.error('Error updating quorum:', error);
        toast.error('Error al actualizar el quorum');
        throw error;
    }
};

onMounted(() => {
    fetchPageData(page.value);
});

</script>

<template>
    <div class="flex flex-col w-full gap-8">
        <SearchBar :onSearch="handleSearch" placeholder="Buscar procesos..." />
        <ViewOptionsToggle :isLoading="isLoading" :items="processes" :headers="headers" label="procesos">
            <template #grid="{ item }">
                <ProcessCard :process="(item as Process)" :path="getRouterLinkByStatusAndID(
                    (item as Process).status, (item as Process).id)" class="process-card"
                    :buttonText="handleGetButtonTextByStatus((item as Process).status)" :disabled="true">
                    <template #options>
                        <Button
                            @click="openQuorumModal(item as Process)"
                            variant="invert"
                            class="px-3 py-1 text-xs"
                            title="Editar Quorum"
                        >
                            <i class="fas fa-percentage mr-1"></i>
                            Quorum
                        </Button>
                    </template>
                </ProcessCard>
            </template>
            <template #list="{ item }">
                <ProcessTile
                    :process="(item as Process)"
                    :path="getRouterLinkByStatusAndID((item as Process).status, (item as Process).id)"
                    :buttonText="handleGetButtonTextByStatus((item as Process).status)"
                    :disabled="true"
                    :showQuorumButton="true"
                    :onQuorumEdit="openQuorumModal"
                />
            </template>
        </ViewOptionsToggle>
        <Pagination :itemCount="processes?.length ?? 0" :totalPages="totalPages" :page="page" @next="handleNextPage"
            @previous="handlePreviousPage" @go-to-page="handleGoToPage" />

        <!-- Modal para editar quorum -->
        <QuorumEditModal
            :isModalOpen="isQuorumModalOpen"
            :process="selectedProcess"
            :onClose="closeQuorumModal"
            :onSave="handleSaveQuorum"
        />
    </div>

</template>
<style scoped>
.process-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  max-width: 100%; 
  height: auto;
  box-sizing: border-box; 
  background-color: white; 
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); 
}

.process-card .card-content {
  overflow: hidden; 
  text-overflow: ellipsis; 
  white-space: nowrap; 
}

.process-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .process-card {
    flex-direction: column;
    flex-wrap: wrap;
  }

  .process-card .card-header {
    flex-direction: column; 
    align-items: flex-start;
  }

  .process-card .card-content {
    white-space: normal; 
  }

  .process-card .card-header h3,
  .process-card .card-header p {
    font-size: 14px; 
  }
}
</style>