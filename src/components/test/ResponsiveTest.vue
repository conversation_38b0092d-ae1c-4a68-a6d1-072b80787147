<template>
  <div class="responsive-test-container p-4">
    <h1 class="text-responsive-lg font-bold mb-4">Test de Responsividad</h1>
    
    <!-- Información de pantalla actual -->
    <div class="bg-blue-100 p-4 rounded-lg mb-6">
      <h2 class="text-responsive-base font-semibold mb-2">Información de Pantalla</h2>
      <p class="text-responsive-sm">Ancho: {{ screenWidth }}px</p>
      <p class="text-responsive-sm">Alto: {{ screenHeight }}px</p>
      <p class="text-responsive-sm">Breakpoint actual: {{ currentBreakpoint }}</p>
    </div>

    <!-- Test de grid responsivo -->
    <div class="mb-6">
      <h2 class="text-responsive-base font-semibold mb-3">Grid Responsivo</h2>
      <div class="grid-responsive-3">
        <div class="bg-green-100 p-4 rounded">Item 1</div>
        <div class="bg-green-200 p-4 rounded">Item 2</div>
        <div class="bg-green-300 p-4 rounded">Item 3</div>
      </div>
    </div>

    <!-- Test de elementos que se ocultan/muestran -->
    <div class="mb-6">
      <h2 class="text-responsive-base font-semibold mb-3">Visibilidad Responsiva</h2>
      <div class="hide-on-mobile bg-red-100 p-2 rounded mb-2">
        Visible solo en tablet y desktop
      </div>
      <div class="hide-on-tablet bg-yellow-100 p-2 rounded mb-2">
        Visible en móvil y desktop
      </div>
      <div class="hide-on-desktop bg-blue-100 p-2 rounded mb-2">
        Visible solo en móvil y tablet
      </div>
    </div>

    <!-- Test de flex responsivo -->
    <div class="mb-6">
      <h2 class="text-responsive-base font-semibold mb-3">Flex Responsivo</h2>
      <div class="flex-responsive">
        <div class="bg-purple-100 p-4 rounded flex-1">Elemento A</div>
        <div class="bg-purple-200 p-4 rounded flex-1">Elemento B</div>
        <div class="bg-purple-300 p-4 rounded flex-1">Elemento C</div>
      </div>
    </div>

    <!-- Test de tabla responsiva -->
    <div class="mb-6">
      <h2 class="text-responsive-base font-semibold mb-3">Tabla Responsiva</h2>
      <div class="table-responsive">
        <table class="w-full border-collapse border border-gray-300">
          <thead>
            <tr class="bg-gray-100">
              <th class="border border-gray-300 p-2">Columna 1</th>
              <th class="border border-gray-300 p-2">Columna 2</th>
              <th class="border border-gray-300 p-2">Columna 3</th>
              <th class="border border-gray-300 p-2">Columna 4</th>
              <th class="border border-gray-300 p-2">Columna 5</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="border border-gray-300 p-2">Dato 1</td>
              <td class="border border-gray-300 p-2">Dato 2</td>
              <td class="border border-gray-300 p-2">Dato 3</td>
              <td class="border border-gray-300 p-2">Dato 4</td>
              <td class="border border-gray-300 p-2">Dato 5</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Botones de prueba -->
    <div class="flex-responsive">
      <button @click="simulateResize(1920, 1080)" class="bg-blue-500 text-white p-2 rounded">
        Simular 1920x1080
      </button>
      <button @click="simulateResize(1366, 768)" class="bg-green-500 text-white p-2 rounded">
        Simular 1366x768
      </button>
      <button @click="simulateResize(1280, 720)" class="bg-yellow-500 text-white p-2 rounded">
        Simular 1280x720
      </button>
      <button @click="resetSize" class="bg-gray-500 text-white p-2 rounded">
        Reset
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';

const screenWidth = ref(0);
const screenHeight = ref(0);

const currentBreakpoint = computed(() => {
  if (screenWidth.value < 480) return 'xs';
  if (screenWidth.value < 640) return 'sm';
  if (screenWidth.value < 768) return 'md';
  if (screenWidth.value < 1024) return 'lg';
  if (screenWidth.value < 1280) return 'xl';
  if (screenWidth.value < 1536) return '2xl';
  return '3xl';
});

const updateScreenSize = () => {
  screenWidth.value = window.innerWidth;
  screenHeight.value = window.innerHeight;
};

const simulateResize = (width: number, height: number) => {
  // Nota: Esta función solo actualiza los valores mostrados
  // En un entorno real, no podemos cambiar el tamaño real de la ventana
  screenWidth.value = width;
  screenHeight.value = height;
  console.log(`Simulando resolución: ${width}x${height}`);
};

const resetSize = () => {
  updateScreenSize();
};

onMounted(() => {
  updateScreenSize();
  window.addEventListener('resize', updateScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize);
});
</script>

<style scoped>
.responsive-test-container {
  max-width: 100%;
  margin: 0 auto;
}

/* Estilos específicos para el test en 720p */
@media (max-width: 1366px) and (max-height: 768px) {
  .responsive-test-container {
    font-size: 0.875rem;
  }
}
</style>
