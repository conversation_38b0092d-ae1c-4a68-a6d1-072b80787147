<template>
  <section id="page">
    <Header></Header>
    <Navbar></Navbar>
    <main id="main" class="[grid-area:main] box-border">
      <slot></slot>
    </main>
  </section>
</template>

<script setup lang="ts">
import Navbar from './NavBar.vue';
import Header from './Header.vue';
</script>

<style scoped>
/* Estilos base para pantallas muy pequeñas (móviles) */
#page {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background-color: #F2F2F2;
}

/* Pantallas pequeñas - layout vertical */
@media (max-width: 767px) {
  #page {
    display: flex;
    flex-direction: column;
  }
}

/* Pantallas medianas - layout híbrido */
@media (min-width: 768px) and (max-width: 1023px) {
  #page {
    display: grid;
    width: 100%;
    height: 100vh;
    grid-template-areas:
      "nav head"
      "nav main";
    grid-template-rows: 10vh 1fr;
    grid-template-columns: 200px 1fr;
    gap: 8px;
    background-color: #F2F2F2;
  }
}

/* Pantallas 720p y similares - layout optimizado */
@media (min-width: 1024px) and (max-width: 1279px) {
  #page {
    display: grid;
    width: 100%;
    height: 100vh;
    grid-template-areas:
      "nav head head"
      "nav main main"
      "nav main main";
    grid-template-rows: 10vh 1fr 1fr;
    grid-template-columns: 220px 1fr 1fr;
    gap: 8px;
    background-color: #F2F2F2;
  }
}

/* Pantallas HD (1280px+) */
@media (min-width: 1280px) and (max-width: 1535px) {
  #page {
    display: grid;
    width: 100%;
    height: 100vh;
    grid-template-areas:
      "nav head head"
      "nav main main"
      "nav main main";
    grid-template-rows: 10vh 1fr 1fr;
    grid-template-columns: 18vw 1fr 1fr;
    gap: 10px;
    background-color: #F2F2F2;
  }
}

/* Pantallas 2XL (1536px+) */
@media (min-width: 1536px) {
  #page {
    display: grid;
    width: 100%;
    height: 100vh;
    grid-template-areas:
      "nav head head"
      "nav main main"
      "nav main main";
    grid-template-rows: 10vh 1fr 1fr;
    grid-template-columns: 16vw 1fr 1fr;
    gap: 10px;
    background-color: #F2F2F2;
  }
}
</style>