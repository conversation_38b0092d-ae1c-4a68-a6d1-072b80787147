<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useNotifications } from '@/hooks/useNotifications'
import { useClickOutside } from '@/hooks/useClickOutside'
import { useAuth } from '@/hooks/useAuth'
const { authData } = useAuth()
const { fetchData, notifications } = useNotifications(
  authData.value?.id ?? 0,
  authData.value?.role ?? ''
)

const isDrawerOpen = ref(false)
const drawerRef = ref<HTMLElement | null>(null)
const selectedNotificationIndex = ref<number | null>(null)

useClickOutside([drawerRef], () => {
  isDrawerOpen.value = false
})

onMounted(() => {
  fetchData()
})
</script>

<template>
  <div
    @click="isDrawerOpen = !isDrawerOpen"
    class="2xl:h-16 h-10 2xl:w-16 w-10 rounded-lg shadow-lg bg-white relative flex items-center justify-center cursor-pointer"
  >
    <i class="fa-solid fa-bell 2xl:text-3xl text-xl text-black/80"> </i>
    <div
      class="absolute font-normal 2xl:top-[12px] top-[1px] 2xl:right-[10px] right-[1px] bg-blue-900 p-2.5 text-white rounded-full 2xl:h-4 2xl:w-4 h-2 w-2 flex items-center justify-center text-xs shadow-sm shadow-blue-300"
    >
      {{ notifications.length }}
    </div>
  </div>

  <article
    :class="{ hidden: !isDrawerOpen }"
    class="fixed top-0 left-0 bottom-0 right-0 flex w-full bg-black/25 z-10"
  ></article>
  <Transition name="slide-fade">
    <aside
      v-if="isDrawerOpen"
      ref="drawerRef"
      class="fixed w-[90vw] sm:w-[60vw] md:w-[40vw] lg:w-[30vw] xl:w-[24vw] flex flex-col gap-8 bg-white z-10 p-6 right-0 top-0 bottom-0"
    >
      <div class="flex flex-col gap-5 w-full">
        <h1 class="flex w-full items-center justify-between text-lg font-semibold text-center">
          <i @click="isDrawerOpen = false" class="fa-solid fa-xmark cursor-pointer"></i>
          Notificaciones
          <div></div>
        </h1>
        <div class="border-b border-black/20"></div>
      </div>
      <ul v-if="notifications.length > 0" class="flex flex-col gap-3">
        <li
          v-for="(notification, index) in notifications"
          :key="index"
          :class="{ selected: index === selectedNotificationIndex }"
          class="flex items-center p-3 gap-4 rounded-md bg-zinc-200/70 text-sm hover:bg-black/10 cursor-pointer transition-all duration-200 border border-zinc-300 shadow-md">
          <div class="flex flex-col w-full text-justify-between">
            <p class="context">
              <span class="">{{ notification }}.</span>
            </p>
          </div>
        </li>
      </ul>
      <p v-else>No hay notificaciones disponibles.</p>
    </aside>
  </Transition>
</template>

<style scoped>
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.slide-fade-enter-to,
.slide-fade-leave-from {
  opacity: 1;
  transform: translateX(0%);
}
</style>
