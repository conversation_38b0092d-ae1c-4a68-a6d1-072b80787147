<script setup lang="ts">
import NotificationsDrawer from '@/components/layout/NotificationsDrawer.vue';
import ProfileTile from '../molecules/ProfileTile.vue';
import { useHeader } from '@/hooks/useHeader';
import { computed } from 'vue';
import TextSkeleton from '../templates/TextSkeleton.vue';
import ConfigUser from '../molecules/ConfigUser.vue'
const { headerTitle, subtitleComponent, isLoading } = useHeader();

const subtitleComponentData = computed(() => {
  if (subtitleComponent.value) {
    return {
      component: subtitleComponent.value.component,
      props: subtitleComponent.value.props
    };
  }
  return { component: null, props: {} };
});

</script>

<template>
  <header class="[grid-area:head] text-black py-4 px-6 flex justify-between items-center relative">
    <div v-if="isLoading">
      <TextSkeleton class="px-2 w-60" />
    </div>
    <div v-else class="flex flex-col px-2 text-lg lg:text-xl xl:text-xl 2xl:text-2xl font-extrabold relative flex-1">
      {{ headerTitle }}
      <Suspense :timeout="0">
        <template #default>
          <component v-if="subtitleComponentData.component" :is="subtitleComponentData.component"
            v-bind="subtitleComponentData.props" class="absolute text-xs lg:text-sm font-semibold top-8 lg:top-10" />
        </template>
        <template #fallback>
          <span class="animate-pulse w-40 lg:w-60 bg-gray-300 rounded-full h-1.5 absolute text-xs lg:text-sm font-semibold top-10 lg:top-12"></span>
        </template>
      </Suspense>
    </div>

    <div class="flex items-center gap-4">
      <NotificationsDrawer />
      <ConfigUser />
      <ProfileTile />

    </div>
  </header>
</template>
