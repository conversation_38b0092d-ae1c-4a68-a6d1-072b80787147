<template>
  <Card class="flex flex-col flex-1">
    <div class="flex flex-col">
      <h3 class="text-lg font-semibold">Observaciones</h3>
      <p class="text-sm text-gray-500">Sugerencias realizadas por el auditor.</p>
      <p v-if="totalComments > 0" class="mt-8 text-lg text-primary-900 font-semibold">
        Este proceso tiene {{ totalComments }} observación(es) disponibles:
      </p>
      <p v-else class="mt-8 text-lg text-gray-500 font-semibold">
        No hay observaciones disponibles para este proceso.
      </p>
      <section v-if="totalComments > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-8 text-white">
        <div v-for="(section, index) in sectionsWithComments" :key="index">
            <ObservationCard
              :title="section.name"
              :observations="section.commentsCount"
              :iconClass="section.icon"
              bgColor="primary-700"
              @click="toggleSection(section.name)"
              :expanded="expandedSection === section.name"
            />
        </div>
      </section>

      <!-- Sección expandida con detalles de observaciones -->
      <section v-if="expandedSection && selectedSectionData" class="mt-8">
        <div class="bg-gray-50 rounded-lg p-6">
          <div class="flex items-center gap-3 mb-4">
            <i :class="selectedSectionData.icon" class="text-2xl" :style="{ color: selectedSectionData.color }"></i>
            <h4 class="text-xl font-semibold text-gray-800">{{ selectedSectionData.name }}</h4>
            <button @click="closeExpandedSection" class="ml-auto text-gray-500 hover:text-gray-700">
              <i class="fas fa-times text-lg"></i>
            </button>
          </div>
          <div class="space-y-4">
            <div v-for="(comment, index) in selectedSectionData.comments" :key="index"
                 class="bg-white rounded-lg p-4 border-l-4 border-primary-500">
              <h5 class="font-semibold text-gray-800 mb-2">{{ comment.evidenceName }}</h5>
              <p class="text-gray-600 text-sm">{{ comment.comment }}</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import ObservationCard from '@/components/molecules/ObservationCard.vue';
import Card from '@/components/atoms/Card.vue';
import { useAuth } from '@/hooks/useAuth';
import { useEvidencesData } from '@/hooks/useEvidencesData';

const { authData } = useAuth();
const { sectionsWithComments, totalComments, expandedSection, fetchEvidenceData, toggleSection } = useEvidencesData();

const actualInstitutionID = ref(-1);

// Computed property para obtener los datos de la sección seleccionada
const selectedSectionData = computed(() => {
  if (!expandedSection.value) return null;
  return sectionsWithComments.value.find(section => section.name === expandedSection.value);
});

// Función para cerrar la sección expandida
const closeExpandedSection = () => {
  expandedSection.value = null;
};

watch(
  authData,
  async (newValue) => {
    if (newValue && newValue.simpleInstitutionDTO) {
      actualInstitutionID.value = newValue.simpleInstitutionDTO.id;
      await fetchEvidenceData(actualInstitutionID.value);
    } else {
      console.error('Error: authData or simpleInstitutionDTO is null or nonexistent');
    }
  },
  { immediate: true }
);
</script>
