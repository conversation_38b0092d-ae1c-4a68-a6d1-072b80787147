<script setup lang="ts">
import type { Process } from '@/models/types';
import Tooltip from '../atoms/Tooltip.vue';
import IconButton from '../atoms/IconButton.vue';
import Button from '../atoms/Button.vue';
import { formatDateForChile } from '@/helpers/formatters';
import { ref } from 'vue';
import { useToast } from 'vue-toast-notification';
const $toast = useToast();


defineProps({
    process: {
        type: Object as () => Process,
        required: true,
    },
    path: {
        type: String,
        required: true,
    },
    buttonText: {
        type: String,
        required: true,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    showQuorumButton: {
        type: Boolean,
        default: false,
    },
    onQuorumEdit: {
        type: Function,
        default: null,
    },
});
const copiedMessage = ref('');

const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        $toast.success('¡Copiado al portapapeles!');
        setTimeout(() => {
            copiedMessage.value = '';
        }, 2000);
    } catch (err) {
        copiedMessage.value = 'Error al copiar';
        setTimeout(() => {
            copiedMessage.value = '';
        }, 2000);
    }
}
</script>

<template>
    <tr class="odd:bg-white even:bg-gray-50 border-b">
        <!-- ID -->
        <th scope="row" class="px-6 py-4">
            #PRC-00{{ process.id }}
        </th>
        <!-- fecha de inicio y fin  -->
        <td class="px-6 py-4">{{ formatDateForChile(process.startDate, "/") }} - {{ formatDateForChile(process.endDate,
            "/") }}</td>
        <!-- institución -->
        <td class="px-6 py-4">
            <div class="flex items-center gap-3">
                <span class="flex text-white items-center justify-center w-9 h-9 rounded-md"
                    :style="{ backgroundColor: process.simpleInstitutionDTO.color }">
                    {{ process.simpleInstitutionDTO.acronym }}
                </span>
                {{ process.simpleInstitutionDTO.name }}
            </div>
        </td>
        <!-- funcionarios -->
        <td class="px-6 py-4">{{ process.employees.length }}</td>
        <!-- ciudad -->
        <td class="px-6 py-4">{{ process.simpleInstitutionDTO.city }}</td>
        <!-- contacto -->
        <td class="px-6 py-4">
            <span class="flex items-center gap-3">
                <Tooltip text="+569 12345678">
                    <IconButton icon="fa-solid fa-phone"  @click="copyToClipboard('+569 12345678')"/>
                </Tooltip>
                <Tooltip text="<EMAIL>">
                    <IconButton icon="fa fa-envelope"  @click="copyToClipboard('<EMAIL>')"/>
                </Tooltip>
            </span>
            <div v-if="copiedMessage" class="text-sm text-green-500 mt-1">{{ copiedMessage }}</div>
        </td>
        <!-- acciones -->
        <td class="px-6 py-4">
            <div class="flex gap-2 items-center">
                <router-link v-if="!disabled" :to="path">
                    <Button variant="secondary">
                        {{ buttonText }}
                    </Button>
                </router-link>
                <Button v-else :disabled="true" variant="secondary">
                    {{ buttonText }}
                </Button>

                <Button
                    v-if="showQuorumButton && onQuorumEdit"
                    @click="onQuorumEdit(process)"
                    variant="invert"
                    class="px-2 py-1 text-xs"
                    title="Editar Quorum"
                >
                    <i class="fas fa-percentage"></i>
                </Button>
            </div>
        </td>
    </tr>

</template>