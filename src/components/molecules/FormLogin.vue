<script lang="ts" setup>
import Button from '../atoms/Button.vue'
import { useAsyncFetch } from '@/hooks/useAsyncFetch'
import Input from '../atoms/Input.vue'
import { useAuth } from '@/hooks/useAuth'
import { loginRules } from '@/models/validation/formRules'
import useVuelidate from '@vuelidate/core'
import { ref, watch } from 'vue'

const loginData = ref({
  username: '',
  password: ''
})
const showPassword = ref<boolean>(false)
const $vLogin = useVuelidate(loginRules, loginData.value)

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const { handleLogin } = useAuth()
const { fetchData, isLoading, error } = useAsyncFetch(handleLogin)

const handleSubmit = async () => {
  const result = await $vLogin.value.$validate()
  if (result) {
    await fetchData({ email: loginData.value.username, password: loginData.value.password })
  }
}
watch(error, (newError) => {
  if (newError) {
    setTimeout(() => {
      error.value = null
    }, 2000)
  }
})
</script>

<template>
  <div class="flex items-center justify-center">
    <div class="w-full flex justify-center">
      <div class="w-full max-w-md p-4 2xl:p-8 bg-white rounded-lg">
        <div class="flex flex-col h-fit w-full gap-0 *:font-montserrat mb-6">
          <h2 class="2xl:header-1 header-4 2xl:mt-0 mt-4 font-bold text-center leading-none">
            Inicio de sesión
          </h2>
          <p class="2xl:header-8 body-4 text-center text-gray-600">
            Ingrese las credenciales de su cuenta
          </p>
        </div>
        <form @submit.prevent="handleSubmit" class="flex flex-col gap-4">
          <!-- Usuario -->
          <div class="flex flex-col">
            <label for="username" class="block text-sm font-medium text-gray-700 mb-1">
              Nombre de Usuario
              <span v-if="$vLogin.username.$error" class="text-red-600"
                >(El nombre es requerido)</span
              >
              <span v-else class="text-primary-400">*</span>
            </label>
            <div class="relative">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i class="fa-regular fa-user text-gray-400"></i>
              </span>
              <input
                id="username"
                type="text"
                v-model="loginData.username"
                :class="[
                  'pl-10 pr-3 py-2 w-full rounded-md border text-sm outline-none',
                  $vLogin.username.$error
                    ? 'border-red-500'
                    : 'border-gray-300 focus:border-primary-400'
                ]"
                placeholder="Ingrese su nombre de usuario"
                autocomplete="username"
              />
            </div>
            <span v-if="$vLogin.username.$error" class="text-xs text-red-500 mt-1">Requerido</span>
          </div>

          <!-- Contraseña -->
          <div class="flex flex-col">
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
              Contraseña
              <span v-if="$vLogin.password.$error" class="text-red-600"
                >(La contraseña es requerida)</span
              >
              <span v-else class="text-primary-400">*</span>
            </label>
            <div class="relative">
              <!-- Icono -->
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i class="fa-solid fa-lock text-gray-400"></i>
              </span>
              <input
                id="password"
                :type="showPassword ? 'text' : 'password'"
                v-model="loginData.password"
                :class="[
                  'pl-10 pr-10 py-2 w-full rounded-md border text-sm outline-none',
                  $vLogin.password.$error
                    ? 'border-red-500'
                    : 'border-gray-300 focus:border-primary-400'
                ]"
                placeholder="Ingrese su contraseña"
                autocomplete="current-password"
              />
              <!-- Mostrar/Ocultar -->
              <button
                type="button"
                @click="togglePassword"
                class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 hover:text-gray-700 focus:outline-none"
                tabindex="-1"
              >
                <i :class="showPassword ? 'fa-solid fa-eye-slash' : 'fa-solid fa-eye'"></i>
              </button>
            </div>
            <span v-if="$vLogin.password.$error" class="text-xs text-red-500 mt-1">Requerido</span>
            <div class="flex justify-end mt-2">
              <router-link
                to="/recoveryPassword"
                class="text-sm text-primary-400 underline hover:underline"
              >
                Recuperar contraseña
              </router-link>
            </div>
          </div>

          <!-- Botón de login -->
          <div v-if="error" class="mt-8">
            <Button
              :loading="isLoading"
              :variant="'missing'"
              type="submit"
              class="font-normal w-full px-4 py-1"
            >
              <span class="font-medium text-base">{{ error.message }}</span>
            </Button>
          </div>
          <div v-else class="mt-8">
            <Button
              :loading="isLoading"
              type="submit"
              class="font-normal w-full px-4 py-1 text-white bg-primary-900 border border-transparent rounded-md"
            >
              <span class="font-medium text-base">Iniciar sesión</span>
            </Button>
          </div>
        </form>

        <div class="flex flex-col items-center justify-center mt-2 text-sm text-gray-600">
          <p>
            ¿No tiene una cuenta?
            <router-link
              :to="{ path: '/register' }"
              class="text-primary-400 underline hover:underline"
              >Solicitar una cuenta.</router-link
            >
          </p>
        </div>

        <div class="w-full flex justify-start mt-20">
          <a href="/" class="flex items-center text-primary-400 hover:underline">
            <i class="fa-solid fa-arrow-left text-neutral-950 mr-1"></i>
            Volver a la página principal
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
