<template>
  <div
    :class="`bg-${bgColor} rounded-3xl p-4 flex flex-col items-center hover:cursor-pointer group 2xl:h-52 h-36`"
    @click="$emit('click')"
  >
    <h3 class="2xl:body-1 body-4 mt-2 text-center">{{ props.title }}</h3>
    <div class="relative mt-4">
      <i :class="iconClass + ' 2xl:text-3xl text-xl transition-opacity duration-300 ease-in-out group-hover:opacity-0'"></i>
      <p class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 text-2xl font-semibold transition-opacity duration-300 ease-in-out">
        VER
      </p>
    </div>
    <p class="2xl:body-1 body-5 mt-auto">{{ props.observations }} observaciones</p>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

const props = defineProps<{
  title: string;
  observations: number;
  iconClass: string;
  bgColor: string;
}>()

const emit = defineEmits<{
  click: []
}>()
</script>
