export interface Process {
    id: number;
    name: string;
    institutionId: number;
    startDate: string;
    endDate: string;
    surveysCompleted: number;
    levelAchieved: string;
    status: string;
    requestStatus: string;
    employees: Employee[];
    nemployees: number;
    employeesNames: string;
    employeesEmails: string;
    employeesRut: string;
    milestones: string[]; // esto debería ser un objeto (timestamp: string, description: string)
    approvalThreshold?: number; // Quorum de aprobación (0-1), por defecto 0.5
    simpleInstitutionDTO: {
        id: number;
        name: string;
        acronym: string;
        color: string;
        city: string;
    };
}

export interface Employee {
    name: string;
    email: string;
    rut: string;
}

export enum ProcessStatus {
    IN_PROGRESS = 'IN_PROGRESS',
    FINISHED = 'FINISHED',
    AUDIT = 'AUDIT',
    SURVEY_FINISHED = 'SURVEY_FINISHED',
    UNINITIATED = 'UNINITIATED',
    APPEALABLE = 'APPEALABLE',
    APPEAL = 'APPEAL'
}

