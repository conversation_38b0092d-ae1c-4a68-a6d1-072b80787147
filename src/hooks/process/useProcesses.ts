import { Logger } from "@/services/logger";
import { assignAuditorToInstitution } from "@/services/api/institutionService";
import { usePagination } from "../usePagination";
import { acceptProcess, assignMaturityModelToProcess, fetchActualProcessByInstitutionID, fetchAllProcessesByInstitutionID, fetchProcessByID, fetchProcessesByAuditorId, fetchProcessesByStatus, finishProcess, rejectProcess, requestProcess, downloadFormatEmployee, downloadManual, auditProcess, updateProcessQuorum } from "@/services/api/processService";
import { createSurvey, fetchSurveyLinkByProcessID } from "@/services/api/surveyService";
import type { Process, ProcessStatus, Recommendation } from "@/models/types";
import { fetchAllMaturityModels } from "@/services/api/maturityModelService";
import type { Ref } from "vue";
import { fetchRecommendationsByProcessId } from "@/services/api/recommendationsService";
import { formatDateToDDMMYYYY } from "@/helpers/formatters";
/**
 * hook para manejar los procesos (instituciones)
 * 
 * @returns objeto con funciones y datos para manejar los procesos
 * 
 * - institutionWithProcesses: instituciones con procesos
 * - page: página actual
 * - fetchPageData: función para obtener los datos de la página actual
 * - totalPages: total de páginas
 * - handleNextPage: función para avanzar a la siguiente página
 * - handlePreviousPage: función para retroceder a la página anterior
 * - handleSearch: función para buscar instituciones
 * - processStatus: estado del proceso
 * - buttonText: texto del botón de acción
 * - canAction: indica si se puede realizar una acción (botones habilitados/deshabilitados)
 * - getRouterLinkByStatusAndID: función para obtener el link de la ruta por estado y ID
 */

interface Props {
    auditorID?: Ref<number>;
    institutionID?: Ref<number>;
    status?: Ref<string>;
}
interface Employee {
    name: string;
    email: string;
    rut: string;
}

export const useProcesses = ({ auditorID, institutionID, status }: Props = {}) => {

    const fetchProcesses = (page: number) => {
        if (institutionID) {
            return fetchAllProcessesByInstitutionID(institutionID.value, page);
        } else if (auditorID) {
            return fetchProcessesByAuditorId(auditorID.value, page);
        } else {
            return fetchProcessesByStatus(status?.value as ProcessStatus, page);
        }
    };

    const { data: processes, page, fetchPageData, totalPages, handleNextPage, handlePreviousPage, isLoading, handleGoToPage } = usePagination(fetchProcesses);

    const handleSearch = async (search: string) => {
        try {
            isLoading.value = true;
            const searchNumber = Number(search);

            if (!isNaN(searchNumber)) {
                await handleSearchProcessByID(searchNumber);
            } else {
                processes.value = [];
            }
        } catch (error: any) {
            Logger.error("Error searching process: " + error.message, { search });
            processes.value = [];
        } finally {
            isLoading.value = false;
        }
    }

    const handleGetButtonTextByStatus = (processStatus: string) => {
        const STATUSES = {
            'AUDIT': 'Auditar',
            'SURVEY_FINISHED': 'Auditar',
            'FINISHED': 'Resumen',
            'IN_PROGRESS': 'Seguimiento',
            'APPEAL': 'Evaluar',
            'APPEALABLE':'Evaluar'
        } as Record<string, string>;

        return STATUSES[processStatus] || 'Seguimiento';
    }

    const canAction = (processStatus: string) => {
        return processStatus !== 'UNINITIATED';
    }

    const getRouterLinkByStatusAndID = (processStatus: string, processID: number) => {
        return `/auditor/${processStatus}/${processID}`;
    }

    const handleGetActualProcessByInstitutionID = async (institutionId: number): Promise<Process | null> => {
        try {
            const process = await fetchActualProcessByInstitutionID(institutionId);
            return process;
        } catch (error: any) { 
            if (error?.message?.includes('No active process found')) {
                return null;
            } else {
                Logger.error('Error fetching actual process for institution ID: ' + institutionId, error);
                throw error;
            }
        }
    }

    const handleGetProcessesByInstitutionID = async (institutionId: number): Promise<Process[]> => {
        try {
            const process = await fetchAllProcessesByInstitutionID(institutionId, page.value);
            return process.data;
        } catch (error) {
            Logger.error('Error fetching processes for institution ID: ' + institutionId, error);
            throw error;
        }
    }
    const simpleDownloadFormatEmployee = async () => {
        try {
          isLoading.value = true;
          const response = await downloadFormatEmployee();
          const url = window.URL.createObjectURL(response);
          
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', 'plantilla_funcionarios.xlsx');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
      
          Logger.debug("Format downloaded successfully");
        } catch (error) {
          Logger.error("Error downloading format", { error });
          throw error;
        } finally {
          isLoading.value = false;
        }
      };

      const simpleDownloadManuals = async (manualName: string, urlManual: string) =>{
        try {
            isLoading.value = true;
            const response = await downloadManual(urlManual);
            const url = window.URL.createObjectURL(response);
            
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', manualName);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        
            Logger.debug("Manual downloaded successfully");
          } catch (error) {
            Logger.error("Error downloading manual", { error });
            throw error;
          } finally {
            isLoading.value = false;
          }
      }

      
    const handleRequestProcess = async (
      institutionId: number,
      name: string,
      employees: Employee[],
      date: string,
      dateEnd: string
    ) => {
      try {
        const formattedDate = formatDateToDDMMYYYY(date)
        const formattedDateEnd = formatDateToDDMMYYYY(dateEnd)

        const jsonData = {
          name,
          employeesName: employees.map((emp) => emp.name),
          employeesEmail: employees.map((emp) => emp.email),
          employeesRut: employees.map((emp) => emp.rut),
          startDate: formattedDate,
          endDate: formattedDateEnd
        }

        await requestProcess(institutionId, jsonData)
      } catch (error) {
        Logger.error('Error requesting process for institution ID: ' + institutionId, error)
        throw error
      }
    }

    const handleSearchProcessByID = async (processID: number): Promise<void> => {
        try {
            isLoading.value = true;
            const formattedID = `#PRC-${processID.toString().padStart(2, '0')}`;
            const processData = (await fetchProcesses(page.value)).data;
            processes.value = processData.filter(process =>
                `#PRC-${process.id.toString().padStart(2, '0')}` === formattedID
            );

        } catch (error) {
            Logger.error('Error searching process by ID: ' + processID, error);
            processes.value = [];
        } finally {
            isLoading.value = false;
        }
    }

    const handleGetProcessByID = async (processID: number): Promise<Process> => {
        try {
            const process = await fetchProcessByID(processID);
            return process;
        } catch (error) {
            Logger.error('Error fetching process by ID: ' + processID, error);
            throw error;
        }
    }

    const handleGetProcessSurveyLink = async (processID: number) => {
        try {
            const surveyLink = await fetchSurveyLinkByProcessID(processID);
            return surveyLink;
        } catch (error) {
            Logger.error('Error fetching survey link for process ID: ' + processID, error);
            throw error;
        }
    }

    const fetchWithTimeout = async (promiseFunction: (arg0: AbortSignal) => any, timeout = 999999) => {
        const controller = new AbortController();
        const signal = controller.signal;

        try {
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Timeout')), timeout);
            });

            const resultPromise = promiseFunction(signal);

            const result = await Promise.race([resultPromise, timeoutPromise]);
            return result;
        } catch (error) {
            if (error === 'AbortError' || error === 'Timeout') {
                Logger.error('The operation has been cancelled due to timeout');
            }
            Logger.error('Error in fetchWithTimeout:', error);
            throw error;
        } finally {
            controller.abort();
        }
    };

    const handleAcceptProcess = async (processID: number, auditorId: number) => {
        try {
            const acceptedProcess = await fetchWithTimeout(() => acceptProcess(processID));

            await fetchWithTimeout(() => 
                assignAuditorToInstitution(acceptedProcess.simpleInstitutionDTO.id, auditorId)
            );
        
            await fetchWithTimeout(() => 
                createSurvey(processID, `${acceptedProcess.simpleInstitutionDTO.name} - ${acceptedProcess.name}`)
            );

        } catch (error) {
            console.trace('Error al aceptar proceso' + processID, error);
            Logger.error('Error accepting process ID: ' + processID, error);
            throw error;
        }
    };

    const handleRejectProcess = async (processID: number) => {
        try {
            await rejectProcess(processID);
        } catch (error) {
            Logger.error('Error rejecting process ID: ' + processID, error);
            throw error;
        }

    }

    const handleFinishProcess = async (processID: number, auditorID: number) => {
        try {
            await finishProcess(processID, auditorID);
        } catch (error) {
            Logger.error('Error finishing process ID: ' + processID + ' for auditor ID: ' + auditorID, error);
            throw error;
        }
    }

    const handleAuditProcess = async (processID: number) => {
        try {
            await auditProcess(processID);
        } catch (error) {
            Logger.error('Error auditing process ID: ' + processID + ' for auditor ID: ' + auditorID, error);
            throw error;
        }
    }

    /**
     * Función para actualizar el quorum de aprobación de un proceso
     * @param processId ID del proceso
     * @param quorum Nuevo quorum de aprobación (0-1)
     */
    const handleUpdateProcessQuorum = async (processId: number, quorum: number): Promise<void> => {
        try {
            await updateProcessQuorum(processId, quorum);
            Logger.info(`Process quorum updated successfully for process ID: ${processId}, new quorum: ${quorum}`);
        } catch (error) {
            Logger.error(`Error updating process quorum for process ID: ${processId}: ${error}`);
            throw error;
        }
    }

    return {
        processes,
        page,
        isLoading,
        fetchPageData,
        handleGetProcessesByInstitutionID,
        handleGetProcessByID,
        handleFinishProcess,
        handleAuditProcess,
        totalPages,
        handleGoToPage,
        handleNextPage,
        handlePreviousPage,
        handleSearch,
        canAction,
        getRouterLinkByStatusAndID,
        handleRequestProcess,
        handleAcceptProcess,
        handleRejectProcess,
        handleGetActualProcessByInstitutionID,
        handleGetProcessSurveyLink,
        handleGetButtonTextByStatus,
        simpleDownloadFormatEmployee,
        simpleDownloadManuals,
        handleUpdateProcessQuorum
    }
}
