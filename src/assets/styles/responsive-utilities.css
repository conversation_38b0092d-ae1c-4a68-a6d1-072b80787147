/* Utilidades responsivas específicas para el proyecto */

/* Breakpoints personalizados */
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  --breakpoint-hd-720: 1280px;
  --breakpoint-hd-ready: 1366px;
}

/* Utilidades para texto responsivo */
.text-responsive-sm {
  font-size: 0.75rem; /* 12px */
}

.text-responsive-base {
  font-size: 0.875rem; /* 14px */
}

.text-responsive-lg {
  font-size: 1rem; /* 16px */
}

@media (min-width: 768px) {
  .text-responsive-sm {
    font-size: 0.875rem; /* 14px */
  }
  
  .text-responsive-base {
    font-size: 1rem; /* 16px */
  }
  
  .text-responsive-lg {
    font-size: 1.125rem; /* 18px */
  }
}

@media (min-width: 1024px) {
  .text-responsive-sm {
    font-size: 1rem; /* 16px */
  }
  
  .text-responsive-base {
    font-size: 1.125rem; /* 18px */
  }
  
  .text-responsive-lg {
    font-size: 1.25rem; /* 20px */
  }
}

/* Utilidades para espaciado responsivo */
.spacing-responsive-sm {
  padding: 0.5rem; /* 8px */
  margin: 0.25rem; /* 4px */
}

.spacing-responsive-md {
  padding: 1rem; /* 16px */
  margin: 0.5rem; /* 8px */
}

.spacing-responsive-lg {
  padding: 1.5rem; /* 24px */
  margin: 0.75rem; /* 12px */
}

@media (min-width: 768px) {
  .spacing-responsive-sm {
    padding: 0.75rem; /* 12px */
    margin: 0.5rem; /* 8px */
  }
  
  .spacing-responsive-md {
    padding: 1.25rem; /* 20px */
    margin: 0.75rem; /* 12px */
  }
  
  .spacing-responsive-lg {
    padding: 2rem; /* 32px */
    margin: 1rem; /* 16px */
  }
}

@media (min-width: 1024px) {
  .spacing-responsive-sm {
    padding: 1rem; /* 16px */
    margin: 0.75rem; /* 12px */
  }
  
  .spacing-responsive-md {
    padding: 1.5rem; /* 24px */
    margin: 1rem; /* 16px */
  }
  
  .spacing-responsive-lg {
    padding: 2.5rem; /* 40px */
    margin: 1.25rem; /* 20px */
  }
}

/* Utilidades para grids responsivos */
.grid-responsive-1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
}

.grid-responsive-2 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.grid-responsive-3 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .grid-responsive-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-responsive-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Utilidades para ocultar/mostrar elementos según el tamaño de pantalla */
.hide-on-mobile {
  display: none;
}

.hide-on-tablet {
  display: block;
}

.hide-on-desktop {
  display: block;
}

@media (min-width: 768px) {
  .hide-on-mobile {
    display: block;
  }
  
  .hide-on-tablet {
    display: none;
  }
}

@media (min-width: 1024px) {
  .hide-on-tablet {
    display: block;
  }
  
  .hide-on-desktop {
    display: none;
  }
}

/* Utilidades específicas para resolución 720p */
@media (max-width: 1366px) and (max-height: 768px) {
  .hd-ready-optimize {
    font-size: 0.875rem;
    padding: 0.5rem;
  }
  
  .hd-ready-compact {
    gap: 0.5rem;
    margin: 0.25rem;
  }
  
  .hd-ready-text-sm {
    font-size: 0.75rem;
    line-height: 1.2;
  }
}

/* Utilidades para contenedores flexibles */
.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (min-width: 768px) {
  .flex-responsive {
    flex-direction: row;
    gap: 1rem;
  }
}

/* Utilidades para anchos responsivos */
.width-responsive-full {
  width: 100%;
}

.width-responsive-auto {
  width: auto;
}

@media (min-width: 768px) {
  .width-responsive-auto {
    width: auto;
  }
}

@media (min-width: 1024px) {
  .width-responsive-auto {
    width: fit-content;
  }
}

/* Correcciones específicas para problemas de overflow */
.overflow-responsive {
  overflow-x: auto;
  overflow-y: hidden;
}

@media (min-width: 1024px) {
  .overflow-responsive {
    overflow: visible;
  }
}

/* Utilidades para tablas responsivas */
.table-responsive {
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

@media (min-width: 1024px) {
  .table-responsive {
    display: table;
    overflow-x: visible;
    white-space: normal;
  }
}
