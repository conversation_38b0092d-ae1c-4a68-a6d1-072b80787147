/** @type {import('tailwindcss').Config} */
import plugin from 'tailwindcss/plugin'
export default {
  content: ["./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",],
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
      // Breakpoints específicos para resoluciones problemáticas
      'hd-ready': '1366px', // 1366x768 (muy común)
      'hd-720': '1280px',   // 1280x720
    },
    extend: {
      colors: {
        'background': '#F2F2F2',
        'primary': {
          '50': '#f4f2ff',
          '100': '#aa8dee',
          '200': '#7f54e5',
          '300': '#5721d6',
          '400': '#40189d',
          '500': '#250e5a',
          '600': '#6832f5',
          '700': '#5920e1',
          '800': '#4b1abd',
          '900': '#40189d',
          '950': '#250c69',
        },
        'secundary':{
          '100':'#fbfbfe',
          '200':'#bbbbee',
          '300':'#7b7bde',
          '400':'#3b3bce',
          '500':'#252594'
        },
        'accent-1':{
          '400':'#42bf9f',
          '500':'#2AA88A',
          '600':'#1e836d'
        },
        'accent-2':{
          '400':'#DA8CC7',
          '500':'#C860AB',
          '600':'#B74B94'
        },
        'accent-3':{
          '400':'#99cae0',
          '500':'#6EB4D2',
          '600':'#3e94b9'
        },
        'accent-4':{
          '400':'#ff6a6a',
          '500':'#f84646',
          '600':'#e51d1d'
        },
        'accent-5':{
          '400':'#ffbf4a',
          '500':'#FFAB2D',
          '600':'#FFAB2D'
        },
        'neutral':{
          '100':'#f2f2f2',
          '200':'#dddddd',
          '300':'#c8c8c8',
          '400':'#b2b2b2',
          '500':'#9d9d9d',
          '600':'#888888',
          '700':'#737373',
          '800':'#5d5d5d',
          '900':'#484848',
          '1000':'#333333'
        },
        'customRed':{
          '100':'#fb3748',
          '200':'#d00416'
        },
        'customYellow':{
          '100':'#ffdb43',
          '200':'#dfb400'
        },
        'customGreen':{
          '100':'#84ebb4',
          '200':'#1fc16b'
        },
        'customPurple': '#40189D',
        'customBlue': '#6EB4D2',
        'customPink': '#C860AB',
      },
      
      fontFamily: {
        montserrat: ['Montserrat', 'sans-serif'],
        lato: ['Lato', 'sans-serif'],
      },
      fontSize: {
        h1: ['2.125rem', { lineHeight: '3.1875rem', fontWeight: '700', letterSpacing: '0px' }],
        h2: ['1.875rem', { lineHeight: '2.8125rem', fontWeight: '700', letterSpacing: '0px' }],
        h3: ['1.75rem', { lineHeight: '2.625rem', fontWeight: '700', letterSpacing: '0px' }],
        h4: ['1.625rem', { lineHeight: '2.4375rem', fontWeight: '700', letterSpacing: '0px' }],
        h5: ['1.5rem', { lineHeight: '2.25rem', fontWeight: '700', letterSpacing: '0px' }],
        h6: ['1.375rem', { lineHeight: '2.0625rem', fontWeight: '700', letterSpacing: '0px' }],
        h7: ['1.25rem', { lineHeight: '1.875rem', fontWeight: '500', letterSpacing: '0px' }],
        h8: ['1.15rem', { lineHeight: '1.6rem', fontWeight: '500', letterSpacing: '0px' }],
        body1: ['1.25rem', { lineHeight: '1.6875rem', fontWeight: '500', letterSpacing: '0px' }],
        body2: ['1.125rem', { lineHeight: '1.5rem', fontWeight: '400', letterSpacing: '0px' }],
        body3: ['1rem', { lineHeight: '1.3125rem', fontWeight: '400', letterSpacing: '0px' }],
        body4: ['0.875rem', { lineHeight: '1.125rem', fontWeight: '400', letterSpacing: '0px' }],
        body5: ['0.75rem', { lineHeight: '0.9375rem', fontWeight: '400', letterSpacing: '0px' }],
        body6: ['0.625rem', { lineHeight: '0.875rem', fontWeight: '400', letterSpacing: '0px' }],
        body7: ['0.5rem', { lineHeight: '0.7rem', fontWeight: '400', letterSpacing: '0px' }],

    }
    },
  },
  plugins: [
    plugin(function({addComponents, theme}){
      addComponents({
        '.header-1':{
          fontFamily: theme('fontFamily')['montserrat'],
          fontSize: theme('fontSize')['h1'][0],
          lineHeight: theme('fontSize')['h1'][1].lineHeight,
          fontWeight: theme('fontSize')['h1'][1].fontWeight,
          letterSpacing: theme('fontSize')['h1'][1].letterSpacing,
        },
        '.header-2': {
          fontFamily: theme('fontFamily')['montserrat'],
          fontSize: theme('fontSize')['h2'][0],
          lineHeight: theme('fontSize')['h2'][1].lineHeight,
          fontWeight: theme('fontSize')['h2'][1].fontWeight,
          letterSpacing: theme('fontSize')['h2'][1].letterSpacing,
        },
        '.header-3':{
          fontFamily: theme('fontFamily')['montserrat'],
          fontSize: theme('fontSize')['h3'][0],
          lineHeight: theme('fontSize')['h3'][1].lineHeight,
          fontWeight: theme('fontSize')['h3'][1].fontWeight,
          letterSpacing: theme('fontSize')['h3'][1].letterSpacing,
        },
        '.header-4':{
          fontFamily: theme('fontFamily')['montserrat'],
          fontSize: theme('fontSize')['h4'][0],
          lineHeight: theme('fontSize')['h4'][1].lineHeight,
          fontWeight: theme('fontSize')['h4'][1].fontWeight,
          letterSpacing: theme('fontSize')['h4'][1].letterSpacing,
        },
        '.header-5':{
          fontFamily: theme('fontFamily')['montserrat'],
          fontSize: theme('fontSize')['h5'][0],
          lineHeight: theme('fontSize')['h5'][1].lineHeight,
          fontWeight: theme('fontSize')['h5'][1].fontWeight,
          letterSpacing: theme('fontSize')['h5'][1].letterSpacing,
        },
        '.header-6':{
          fontFamily: theme('fontFamily')['montserrat'],
          fontSize: theme('fontSize')['h6'][0],
          lineHeight: theme('fontSize')['h6'][1].lineHeight,
          fontWeight: theme('fontSize')['h6'][1].fontWeight,
          letterSpacing: theme('fontSize')['h6'][1].letterSpacing,
        },
        '.header-7':{
          fontFamily: theme('fontFamily')['montserrat'],
          fontSize: theme('fontSize')['h7'][0],
          lineHeight: theme('fontSize')['h7'][1].lineHeight,
          fontWeight: theme('fontSize')['h7'][1].fontWeight,
          letterSpacing: theme('fontSize')['h7'][1].letterSpacing,
        },
       '.header-8':{
        fontFamily: theme('fontFamily')['montserrat'],
          fontSize: theme('fontSize')['h8'][0],
          lineHeight: theme('fontSize')['h8'][1].lineHeight,
          fontWeight: theme('fontSize')['h8'][1].fontWeight,
          letterSpacing: theme('fontSize')['h8'][1].letterSpacing,
       },
        '.body-1':{
          fontFamily: theme('fontFamily')['lato'],
          fontSize: theme('fontSize')['body1'][0],
          lineHeight: theme('fontSize')['body1'][1].lineHeight,
          fontWeight: theme('fontSize')['body1'][1].fontWeight,
          letterSpacing: theme('fontSize')['body1'][1].letterSpacing,
        },
        '.body-2':{
          fontFamily: theme('fontFamily')['lato'],
          fontSize: theme('fontSize')['body2'][0],
          lineHeight: theme('fontSize')['body2'][1].lineHeight,
          fontWeight: theme('fontSize')['body2'][1].fontWeight,
          letterSpacing: theme('fontSize')['body2'][1].letterSpacing,
        },
        '.body-3':{
          fontFamily: theme('fontFamily')['lato'],
          fontSize: theme('fontSize')['body3'][0],
          lineHeight: theme('fontSize')['body3'][1].lineHeight,
          fontWeight: theme('fontSize')['body3'][1].fontWeight,
          letterSpacing: theme('fontSize')['body3'][1].letterSpacing,
        },
        '.body-4':{
          fontFamily: theme('fontFamily')['lato'],
          fontSize: theme('fontSize')['body4'][0],
          lineHeight: theme('fontSize')['body4'][1].lineHeight,
          fontWeight: theme('fontSize')['body4'][1].fontWeight,
          letterSpacing: theme('fontSize')['body4'][1].letterSpacing,
        },
        '.body-5':{
          fontFamily: theme('fontFamily')['lato'],
          fontSize: theme('fontSize')['body5'][0],
          lineHeight: theme('fontSize')['body5'][1].lineHeight,
          fontWeight: theme('fontSize')['body5'][1].fontWeight,
          letterSpacing: theme('fontSize')['body5'][1].letterSpacing,
        },
        '.body-6':{
          fontFamily: theme('fontFamily')['lato'],
          fontSize: theme('fontSize')['body6'][0],
          lineHeight: theme('fontSize')['body6'][1].lineHeight,
          fontWeight: theme('fontSize')['body6'][1].fontWeight,
          letterSpacing: theme('fontSize')['body6'][1].letterSpacing,
        },
        '.body-7':{
          fontFamily: theme('fontFamily')['lato'],
          fontSize: theme('fontSize')['body7'][0],
          lineHeight: theme('fontSize')['body7'][1].lineHeight,
          fontWeight: theme('fontSize')['body7'][1].fontWeight,
          letterSpacing: theme('fontSize')['body7'][1].letterSpacing,
        }
      })
    })

  
  ],
}